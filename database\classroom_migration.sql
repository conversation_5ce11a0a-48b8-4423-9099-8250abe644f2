-- Classroom Module Migration Script
-- This script creates the new classroom-based structure to fix data consistency issues
-- 
-- IMPORTANT: This script should be run through the maintenance module interface
-- Make sure to backup your database before running this migration!

SET FOREIGN_KEY_CHECKS=0;

-- =====================================================
-- 1. CREATE NEW CLASSROOM TABLES
-- =====================================================

-- Main classroom table - each classroom is tied to specific semester and academic year
DROP TABLE IF EXISTS `ruang_kelas`;
CREATE TABLE `ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_ruang_kelas` varchar(100) NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `semester` enum('1','2') NOT NULL,
  `tahun_ajaran` varchar(9) NOT NULL,
  `tingkat_id` int(11) DEFAULT NULL,
  `jurusan_id` int(11) DEFAULT NULL,
  `guru_wali_id` int(11) DEFAULT NULL,
  `kapasitas_maksimal` int(11) DEFAULT 40,
  `status` enum('aktif','nonaktif') DEFAULT 'aktif',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fk_ruang_kelas_tingkat` (`tingkat_id`),
  KEY `fk_ruang_kelas_jurusan` (`jurusan_id`),
  KEY `fk_ruang_kelas_guru_wali` (`guru_wali_id`),
  KEY `idx_semester_tahun` (`semester`, `tahun_ajaran`),
  CONSTRAINT `fk_ruang_kelas_tingkat` FOREIGN KEY (`tingkat_id`) REFERENCES `tingkat` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_ruang_kelas_jurusan` FOREIGN KEY (`jurusan_id`) REFERENCES `jurusan` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_ruang_kelas_guru_wali` FOREIGN KEY (`guru_wali_id`) REFERENCES `guru` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Student-classroom assignment table - manages which students are in which classrooms
DROP TABLE IF EXISTS `siswa_ruang_kelas`;
CREATE TABLE `siswa_ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `siswa_id` int(11) NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `tanggal_masuk` date NOT NULL,
  `tanggal_keluar` date DEFAULT NULL,
  `status` enum('aktif','pindah','lulus','keluar') DEFAULT 'aktif',
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_siswa_ruang_aktif` (`siswa_id`, `ruang_kelas_id`),
  KEY `fk_siswa_ruang_siswa` (`siswa_id`),
  KEY `fk_siswa_ruang_kelas` (`ruang_kelas_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_siswa_ruang_siswa` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_siswa_ruang_kelas` FOREIGN KEY (`ruang_kelas_id`) REFERENCES `ruang_kelas` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 2. MODIFY EXISTING TABLES
-- =====================================================

-- Remove kelas_id from siswa table (will be handled by siswa_ruang_kelas)
-- We'll keep it for now during migration, then remove it later
ALTER TABLE `siswa` ADD COLUMN `kelas_id_backup` int(11) DEFAULT NULL;
UPDATE `siswa` SET `kelas_id_backup` = `kelas_id`;

-- Add ruang_kelas_id to attendance table
ALTER TABLE `absensi` ADD COLUMN `ruang_kelas_id` int(11) DEFAULT NULL;
ALTER TABLE `absensi` ADD KEY `fk_absensi_ruang_kelas` (`ruang_kelas_id`);

-- Add ruang_kelas_id to assignments table  
ALTER TABLE `tugas` ADD COLUMN `ruang_kelas_id` int(11) DEFAULT NULL;
ALTER TABLE `tugas` ADD KEY `fk_tugas_ruang_kelas` (`ruang_kelas_id`);

-- Add ruang_kelas_id to grades table (through siswa_ruang_kelas relationship)
-- We'll handle this through joins rather than direct foreign key

-- =====================================================
-- 3. CREATE MIGRATION TRACKING TABLE
-- =====================================================

DROP TABLE IF EXISTS `classroom_migration_log`;
CREATE TABLE `classroom_migration_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `migration_step` varchar(100) NOT NULL,
  `status` enum('pending','running','completed','failed') DEFAULT 'pending',
  `records_processed` int(11) DEFAULT 0,
  `total_records` int(11) DEFAULT 0,
  `error_message` text DEFAULT NULL,
  `started_at` timestamp NULL DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

SET FOREIGN_KEY_CHECKS=1;

-- =====================================================
-- 4. INSERT INITIAL MIGRATION LOG ENTRIES
-- =====================================================

INSERT INTO `classroom_migration_log` (`migration_step`, `status`) VALUES
('create_tables', 'completed'),
('migrate_classrooms', 'pending'),
('migrate_student_assignments', 'pending'),
('migrate_attendance_data', 'pending'),
('migrate_assignment_data', 'pending'),
('migrate_grade_data', 'pending'),
('cleanup_old_structure', 'pending'),
('verify_data_integrity', 'pending');

-- =====================================================
-- NOTES FOR MIGRATION PROCESS:
-- =====================================================
--
-- This script only creates the new structure. The actual data migration
-- will be handled by the PHP migration interface which will:
--
-- 1. Create classrooms from existing kelas data for each semester/year combination
-- 2. Assign students to appropriate classrooms based on their current class and active periods
-- 3. Update attendance records to reference the new classroom structure
-- 4. Update assignment records to reference the new classroom structure
-- 5. Ensure grade data can be accessed through the new classroom relationships
-- 6. Remove old foreign key constraints and unused columns
-- 7. Verify data integrity across all modules
--
-- The migration will preserve all historical data while fixing the consistency issues.
--
-- NEW WORKFLOW AFTER MIGRATION:
-- 1. Admin changes semester/academic year settings
-- 2. Admin creates new classrooms for the new period
-- 3. Admin assigns students to classrooms (no more grade promotion needed)
-- 4. All modules (attendance, assignments, grades) work with classroom assignments
-- 5. Historical data remains accessible by selecting previous periods
