-- Manual Classroom Tables Creation
-- Copy and paste this SQL into phpMyAdmin or run it directly

-- Create ruang_kelas table
CREATE TABLE IF NOT EXISTS `ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_ruang_kelas` varchar(100) NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `semester` enum('1','2') NOT NULL,
  `tahun_ajaran` varchar(9) NOT NULL,
  `tingkat_id` int(11) DEFAULT NULL,
  `jurusan_id` int(11) DEFAULT NULL,
  `guru_wali_id` int(11) DEFAULT NULL,
  `kapasitas_maksimal` int(11) DEFAULT 40,
  `status` enum('aktif','nonaktif') DEFAULT 'aktif',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_semester_tahun` (`semester`, `tahun_ajaran`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create siswa_ruang_kelas table
CREATE TABLE IF NOT EXISTS `siswa_ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `siswa_id` int(11) NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `tanggal_masuk` date NOT NULL,
  `tanggal_keluar` date DEFAULT NULL,
  `status` enum('aktif','pindah','lulus','keluar') DEFAULT 'aktif',
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_siswa_ruang_aktif` (`siswa_id`, `ruang_kelas_id`),
  KEY `fk_siswa_ruang_siswa` (`siswa_id`),
  KEY `fk_siswa_ruang_kelas` (`ruang_kelas_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create migration log table
CREATE TABLE IF NOT EXISTS `classroom_migration_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `migration_step` varchar(100) NOT NULL,
  `status` enum('pending','running','completed','failed') DEFAULT 'pending',
  `records_processed` int(11) DEFAULT 0,
  `total_records` int(11) DEFAULT 0,
  `error_message` text DEFAULT NULL,
  `started_at` timestamp NULL DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Add columns to existing tables
ALTER TABLE `siswa` ADD COLUMN IF NOT EXISTS `kelas_id_backup` int(11) DEFAULT NULL;
ALTER TABLE `absensi` ADD COLUMN IF NOT EXISTS `ruang_kelas_id` int(11) DEFAULT NULL;
ALTER TABLE `tugas` ADD COLUMN IF NOT EXISTS `ruang_kelas_id` int(11) DEFAULT NULL;

-- Add indexes
ALTER TABLE `absensi` ADD KEY IF NOT EXISTS `fk_absensi_ruang_kelas` (`ruang_kelas_id`);
ALTER TABLE `tugas` ADD KEY IF NOT EXISTS `fk_tugas_ruang_kelas` (`ruang_kelas_id`);

-- Insert migration log entries
INSERT IGNORE INTO `classroom_migration_log` (`migration_step`, `status`) VALUES
('create_tables', 'completed'),
('migrate_classrooms', 'pending'),
('migrate_student_assignments', 'pending'),
('migrate_attendance_data', 'pending'),
('migrate_assignment_data', 'pending'),
('migrate_grade_data', 'pending'),
('cleanup_old_structure', 'pending'),
('verify_data_integrity', 'pending');

-- Update backup data
UPDATE `siswa` SET `kelas_id_backup` = `kelas_id` WHERE `kelas_id` IS NOT NULL AND `kelas_id_backup` IS NULL;
