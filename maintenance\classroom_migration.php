<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../template/header.php';
require_once __DIR__ . '/../models/ClassroomMigration.php';

// <PERSON>ya admin yang dapat mengakses halaman ini
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

$migrationModel = new ClassroomMigration();
$message = '';
$error = '';

// Handle migration actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_backup':
                $result = $migrationModel->createBackup();
                if ($result['success']) {
                    $message = "Backup berhasil dibuat: " . basename($result['file']);
                } else {
                    $error = "Gagal membuat backup: " . $result['message'];
                }
                break;
                
            case 'execute_initial':
                $result = $migrationModel->executeInitialMigration();
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
                
            case 'run_migration':
                $result = $migrationModel->runMigration();
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
        }
    }
}

// Get migration status
$migrationStatus = $migrationModel->getMigrationStatus();
$isMigrationNeeded = $migrationModel->isMigrationNeeded();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-door-open"></i> Migrasi Sistem Ruang Kelas</h2>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($message) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Migration Overview -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">Gambaran Umum Migrasi</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Masalah yang Akan Dipecahkan:</h6>
                    <ul>
                        <li>Data siswa hilang saat lulus/naik kelas</li>
                        <li>Data tugas dan nilai tercampur antar periode</li>
                        <li>Tidak ada pemisahan data berdasarkan semester/tahun ajaran</li>
                        <li>Siswa mewarisi data dari siswa yang sudah lulus</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Solusi yang Akan Diterapkan:</h6>
                    <ul>
                        <li>Sistem ruang kelas terpisah per periode</li>
                        <li>Data historis tetap terjaga dan dapat diakses</li>
                        <li>Siswa hanya menyimpan data dasar (nama, NIS, dll)</li>
                        <li>Semua modul menggunakan referensi ruang kelas</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <?php if ($isMigrationNeeded): ?>
        <!-- Pre-Migration Steps -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Langkah Persiapan Migrasi
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <strong>PENTING:</strong> Migrasi ini akan mengubah struktur database secara signifikan. 
                    Pastikan untuk membuat backup terlebih dahulu!
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-database fa-3x mb-3 text-primary"></i>
                                <h6>1. Buat Backup Database</h6>
                                <p class="small text-muted">Backup otomatis sebelum migrasi</p>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="create_backup">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-download"></i> Buat Backup
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-cogs fa-3x mb-3 text-info"></i>
                                <h6>2. Buat Struktur Tabel Baru</h6>
                                <p class="small text-muted">Membuat tabel ruang_kelas dan tabel pendukung</p>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="execute_initial">
                                    <button type="submit" class="btn btn-info">
                                        <i class="fas fa-plus"></i> Buat Struktur
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Migration Execution -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-rocket"></i> Eksekusi Migrasi
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>Catatan:</strong> Proses migrasi akan berjalan otomatis dan memindahkan semua data ke struktur baru.
                    Proses ini mungkin memakan waktu beberapa menit tergantung jumlah data.
                </div>
                
                <div class="text-center">
                    <form method="POST" onsubmit="return confirm('Apakah Anda yakin ingin memulai migrasi? Pastikan backup sudah dibuat!')">
                        <input type="hidden" name="action" value="run_migration">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-play"></i> Mulai Migrasi Data
                        </button>
                    </form>
                </div>
            </div>
        </div>

    <?php else: ?>
        <!-- Migration Status -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-check-circle"></i> Status Migrasi
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($migrationStatus)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Struktur tabel ruang kelas sudah ada, tetapi belum ada log migrasi.
                        Sistem mungkin sudah dimigrasi sebelumnya atau dibuat dengan struktur baru.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Langkah Migrasi</th>
                                    <th>Status</th>
                                    <th>Progress</th>
                                    <th>Waktu</th>
                                    <th>Keterangan</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($migrationStatus as $step): ?>
                                    <tr>
                                        <td><?= htmlspecialchars(str_replace('_', ' ', ucwords($step['migration_step'], '_'))) ?></td>
                                        <td>
                                            <?php
                                            $statusClass = '';
                                            $statusIcon = '';
                                            switch ($step['status']) {
                                                case 'completed':
                                                    $statusClass = 'success';
                                                    $statusIcon = 'check-circle';
                                                    break;
                                                case 'running':
                                                    $statusClass = 'warning';
                                                    $statusIcon = 'spinner fa-spin';
                                                    break;
                                                case 'failed':
                                                    $statusClass = 'danger';
                                                    $statusIcon = 'times-circle';
                                                    break;
                                                default:
                                                    $statusClass = 'secondary';
                                                    $statusIcon = 'clock';
                                            }
                                            ?>
                                            <span class="badge bg-<?= $statusClass ?>">
                                                <i class="fas fa-<?= $statusIcon ?>"></i>
                                                <?= ucfirst($step['status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($step['total_records'] > 0): ?>
                                                <?= $step['records_processed'] ?> / <?= $step['total_records'] ?>
                                                <div class="progress mt-1" style="height: 5px;">
                                                    <div class="progress-bar" style="width: <?= ($step['records_processed'] / $step['total_records']) * 100 ?>%"></div>
                                                </div>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($step['completed_at']): ?>
                                                <small><?= date('d/m/Y H:i', strtotime($step['completed_at'])) ?></small>
                                            <?php elseif ($step['started_at']): ?>
                                                <small>Dimulai: <?= date('d/m/Y H:i', strtotime($step['started_at'])) ?></small>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($step['error_message']): ?>
                                                <small class="text-muted"><?= htmlspecialchars($step['error_message']) ?></small>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Post-Migration Actions -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tasks"></i> Langkah Selanjutnya
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-door-open fa-2x mb-2 text-primary"></i>
                                <h6>Kelola Ruang Kelas</h6>
                                <p class="small text-muted">Buat dan kelola ruang kelas baru</p>
                                <a href="/absen/ruang_kelas/" class="btn btn-primary btn-sm">
                                    <i class="fas fa-arrow-right"></i> Buka Modul
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2 text-success"></i>
                                <h6>Assign Siswa</h6>
                                <p class="small text-muted">Tempatkan siswa ke ruang kelas</p>
                                <a href="/absen/ruang_kelas/" class="btn btn-success btn-sm">
                                    <i class="fas fa-arrow-right"></i> Mulai Assign
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-book fa-2x mb-2 text-info"></i>
                                <h6>Baca Dokumentasi</h6>
                                <p class="small text-muted">Pelajari cara menggunakan sistem baru</p>
                                <button class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#documentationModal">
                                    <i class="fas fa-book-open"></i> Baca Panduan
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Documentation Modal -->
<div class="modal fade" id="documentationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Panduan Sistem Ruang Kelas</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Workflow Baru:</h6>
                <ol>
                    <li><strong>Ubah Periode:</strong> Admin mengubah semester dan tahun ajaran di pengaturan</li>
                    <li><strong>Buat Ruang Kelas:</strong> Admin membuat ruang kelas baru untuk periode tersebut</li>
                    <li><strong>Assign Siswa:</strong> Admin menempatkan siswa ke ruang kelas yang sesuai</li>
                    <li><strong>Gunakan Sistem:</strong> Semua modul (absensi, tugas, nilai) otomatis menggunakan ruang kelas</li>
                </ol>

                <h6>Keuntungan Sistem Baru:</h6>
                <ul>
                    <li>Data historis tetap terjaga untuk setiap periode</li>
                    <li>Tidak ada lagi masalah data hilang saat naik kelas</li>
                    <li>Tidak perlu proses naik kelas atau kelulusan</li>
                    <li>Data dapat diakses berdasarkan periode tertentu</li>
                </ul>

                <h6>Catatan Penting:</h6>
                <ul>
                    <li>Setiap ruang kelas terikat pada semester dan tahun ajaran tertentu</li>
                    <li>Siswa dapat berada di ruang kelas yang berbeda untuk periode yang berbeda</li>
                    <li>Data lama tetap dapat diakses dengan memilih periode yang sesuai</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
