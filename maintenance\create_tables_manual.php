<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../template/header.php';
require_once __DIR__ . '/../config/database.php';

// Hanya admin yang dapat mengakses halaman ini
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

$message = '';
$error = '';

// Handle manual table creation
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_tables'])) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        // SQL statements to create tables
        $sql_statements = [
            // Create ruang_kelas table
            "CREATE TABLE IF NOT EXISTS `ruang_kelas` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `nama_ruang_kelas` varchar(100) NOT NULL,
              `deskripsi` text DEFAULT NULL,
              `semester` enum('1','2') NOT NULL,
              `tahun_ajaran` varchar(9) NOT NULL,
              `tingkat_id` int(11) DEFAULT NULL,
              `jurusan_id` int(11) DEFAULT NULL,
              `guru_wali_id` int(11) DEFAULT NULL,
              `kapasitas_maksimal` int(11) DEFAULT 40,
              `status` enum('aktif','nonaktif') DEFAULT 'aktif',
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `idx_semester_tahun` (`semester`, `tahun_ajaran`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",
            
            // Create siswa_ruang_kelas table
            "CREATE TABLE IF NOT EXISTS `siswa_ruang_kelas` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `siswa_id` int(11) NOT NULL,
              `ruang_kelas_id` int(11) NOT NULL,
              `tanggal_masuk` date NOT NULL,
              `tanggal_keluar` date DEFAULT NULL,
              `status` enum('aktif','pindah','lulus','keluar') DEFAULT 'aktif',
              `keterangan` text DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_siswa_ruang_aktif` (`siswa_id`, `ruang_kelas_id`),
              KEY `fk_siswa_ruang_siswa` (`siswa_id`),
              KEY `fk_siswa_ruang_kelas` (`ruang_kelas_id`),
              KEY `idx_status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",
            
            // Create migration log table
            "CREATE TABLE IF NOT EXISTS `classroom_migration_log` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `migration_step` varchar(100) NOT NULL,
              `status` enum('pending','running','completed','failed') DEFAULT 'pending',
              `records_processed` int(11) DEFAULT 0,
              `total_records` int(11) DEFAULT 0,
              `error_message` text DEFAULT NULL,
              `started_at` timestamp NULL DEFAULT NULL,
              `completed_at` timestamp NULL DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci"
        ];
        
        $created_tables = 0;
        foreach ($sql_statements as $sql) {
            try {
                $conn->exec($sql);
                $created_tables++;
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'already exists') === false) {
                    throw $e;
                }
            }
        }
        
        // Add columns to existing tables
        $alter_statements = [
            "ALTER TABLE `siswa` ADD COLUMN `kelas_id_backup` int(11) DEFAULT NULL",
            "ALTER TABLE `absensi` ADD COLUMN `ruang_kelas_id` int(11) DEFAULT NULL",
            "ALTER TABLE `tugas` ADD COLUMN `ruang_kelas_id` int(11) DEFAULT NULL",
            "ALTER TABLE `nilai` ADD COLUMN `ruang_kelas_id` int(11) DEFAULT NULL",
            "ALTER TABLE `jadwal_pelajaran` ADD COLUMN `ruang_kelas_id` int(11) DEFAULT NULL",
            "ALTER TABLE `nilai_sikap` ADD COLUMN `ruang_kelas_id` int(11) DEFAULT NULL",
            "ALTER TABLE `tugas_tambahan` ADD COLUMN `ruang_kelas_id` int(11) DEFAULT NULL"
        ];
        
        $added_columns = 0;
        foreach ($alter_statements as $sql) {
            try {
                $conn->exec($sql);
                $added_columns++;
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                    // Log but don't fail
                    error_log("Column add failed: " . $e->getMessage());
                }
            }
        }
        
        // Add indexes
        $index_statements = [
            "ALTER TABLE `absensi` ADD KEY `fk_absensi_ruang_kelas` (`ruang_kelas_id`)",
            "ALTER TABLE `tugas` ADD KEY `fk_tugas_ruang_kelas` (`ruang_kelas_id`)",
            "ALTER TABLE `nilai` ADD KEY `fk_nilai_ruang_kelas` (`ruang_kelas_id`)",
            "ALTER TABLE `jadwal_pelajaran` ADD KEY `fk_jadwal_ruang_kelas` (`ruang_kelas_id`)",
            "ALTER TABLE `nilai_sikap` ADD KEY `fk_nilai_sikap_ruang_kelas` (`ruang_kelas_id`)",
            "ALTER TABLE `tugas_tambahan` ADD KEY `fk_tugas_tambahan_ruang_kelas` (`ruang_kelas_id`)"
        ];
        
        foreach ($index_statements as $sql) {
            try {
                $conn->exec($sql);
            } catch (PDOException $e) {
                // Ignore duplicate key errors
                if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                    error_log("Index add failed: " . $e->getMessage());
                }
            }
        }
        
        // Insert migration log
        $log_sql = "INSERT IGNORE INTO `classroom_migration_log` (`migration_step`, `status`) VALUES
                   ('create_tables', 'completed'),
                   ('migrate_classrooms', 'pending'),
                   ('migrate_student_assignments', 'pending'),
                   ('migrate_attendance_data', 'pending'),
                   ('migrate_assignment_data', 'pending'),
                   ('migrate_grade_data', 'pending'),
                   ('cleanup_old_structure', 'pending'),
                   ('verify_data_integrity', 'pending')";
        
        $conn->exec($log_sql);
        
        // Update backup data
        $conn->exec("UPDATE `siswa` SET `kelas_id_backup` = `kelas_id` WHERE `kelas_id` IS NOT NULL AND `kelas_id_backup` IS NULL");
        
        $message = "Tabel berhasil dibuat! Created $created_tables tables, added $added_columns columns. Sekarang Anda dapat menggunakan modul Ruang Kelas.";
        
    } catch (Exception $e) {
        $error = "Gagal membuat tabel: " . $e->getMessage();
    }
}

// Check if tables exist
$database = new Database();
$conn = $database->getConnection();

$tables_exist = [];
$check_tables = ['ruang_kelas', 'siswa_ruang_kelas', 'classroom_migration_log'];

foreach ($check_tables as $table) {
    try {
        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
        $tables_exist[$table] = $stmt->rowCount() > 0;
    } catch (Exception $e) {
        $tables_exist[$table] = false;
    }
}

$all_tables_exist = array_reduce($tables_exist, function($carry, $item) {
    return $carry && $item;
}, true);
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-tools"></i> Buat Tabel Ruang Kelas Manual</h2>
        <a href="classroom_migration.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali ke Migration
        </a>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($message) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Status Tabel</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Nama Tabel</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($tables_exist as $table => $exists): ?>
                            <tr>
                                <td><code><?= htmlspecialchars($table) ?></code></td>
                                <td>
                                    <?php if ($exists): ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Sudah Ada
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times"></i> Belum Ada
                                        </span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <?php if (!$all_tables_exist): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Tabel belum lengkap!</strong> Klik tombol di bawah untuk membuat tabel yang diperlukan.
                </div>
                
                <form method="POST" onsubmit="return confirm('Apakah Anda yakin ingin membuat tabel-tabel ini?')">
                    <button type="submit" name="create_tables" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus"></i> Buat Tabel Sekarang
                    </button>
                </form>
            <?php else: ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <strong>Semua tabel sudah ada!</strong> Anda dapat menggunakan modul Ruang Kelas sekarang.
                </div>
                
                <div class="text-center">
                    <a href="/absen/ruang_kelas/" class="btn btn-success btn-lg">
                        <i class="fas fa-door-open"></i> Buka Modul Ruang Kelas
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
