<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../template/header.php';

// <PERSON>ya admin yang dapat mengakses halaman ini
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Pemeliharaan Database</h2>
        <a href="/absen/" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali ke Dashboard
        </a>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Pemeliharaan Database</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-download fa-3x mb-3 text-primary"></i>
                                    <h5 class="card-title">Backup Database</h5>
                                    <p class="card-text">Buat cadangan database untuk mencegah kehilangan data.</p>
                                    <a href="backup.php" class="btn btn-primary">
                                        <i class="fas fa-arrow-right"></i> Mulai Backup
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-undo fa-3x mb-3 text-warning"></i>
                                    <h5 class="card-title">Pulihkan Database</h5>
                                    <p class="card-text">Pulihkan database dari file backup yang tersedia.</p>
                                    <a href="restore.php" class="btn btn-warning">
                                        <i class="fas fa-arrow-right"></i> Mulai Pemulihan
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-broom fa-3x mb-3 text-danger"></i>
                                    <h5 class="card-title">Bersihkan Database</h5>
                                    <p class="card-text">Bersihkan data-data yang sudah tidak diperlukan.</p>
                                    <a href="clean.php" class="btn btn-danger">
                                        <i class="fas fa-arrow-right"></i> Mulai Pembersihan
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Panduan Penggunaan -->
    <div class="card mt-4">
        <div class="card-body">
            <h5 class="card-title">
                <i class="fas fa-info-circle"></i> Panduan Penggunaan
            </h5>
            <div class="alert alert-info">
                <h6><i class="fas fa-database"></i> Backup Database</h6>
                <ul>
                    <li>Backup akan disimpan di folder <code>database/backups</code></li>
                    <li>Format nama file: <code>backup_YYYY-MM-DD_HH-mm-ss.sql</code></li>
                    <li>Backup mencakup struktur dan data dari semua tabel</li>
                </ul>
            </div>
            <div class="alert alert-warning">
                <h6><i class="fas fa-undo"></i> Pulihkan Database</h6>
                <ul>
                    <li>Pilih file backup (.sql) untuk memulihkan database</li>
                    <li>Proses ini akan menimpa data yang ada saat ini</li>
                    <li>Pastikan file backup valid dan tidak rusak</li>
                </ul>
            </div>
            <div class="alert alert-danger">
                <h6><i class="fas fa-broom"></i> Bersihkan Database</h6>
                <ul>
                    <li>Menghapus semua data kecuali akun admin</li>
                    <li>Tindakan ini tidak dapat dibatalkan</li>
                    <li>Selalu buat backup sebelum membersihkan database</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
