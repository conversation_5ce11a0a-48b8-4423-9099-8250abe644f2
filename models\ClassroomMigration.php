<?php
require_once __DIR__ . '/../config/database.php';

class ClassroomMigration {
    private $conn;
    private $log_table = "classroom_migration_log";

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // Check if migration is needed
    public function isMigrationNeeded() {
        try {
            // Check if new tables exist
            $query = "SHOW TABLES LIKE 'ruang_kelas'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            
            return $stmt->rowCount() == 0;
        } catch (Exception $e) {
            return true;
        }
    }

    // Get migration status
    public function getMigrationStatus() {
        try {
            $query = "SELECT * FROM " . $this->log_table . " ORDER BY id ASC";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return [];
        }
    }

    // Update migration log
    private function updateMigrationLog($step, $status, $records_processed = 0, $total_records = 0, $error_message = null) {
        try {
            $query = "UPDATE " . $this->log_table . " 
                     SET status = :status, 
                         records_processed = :records_processed,
                         total_records = :total_records,
                         error_message = :error_message,
                         started_at = CASE WHEN status = 'pending' THEN NOW() ELSE started_at END,
                         completed_at = CASE WHEN :status IN ('completed', 'failed') THEN NOW() ELSE completed_at END
                     WHERE migration_step = :step";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':records_processed', $records_processed);
            $stmt->bindParam(':total_records', $total_records);
            $stmt->bindParam(':error_message', $error_message);
            $stmt->bindParam(':step', $step);
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Migration log update failed: " . $e->getMessage());
            return false;
        }
    }

    // Run the complete migration
    public function runMigration() {
        try {
            $this->conn->beginTransaction();

            // Step 1: Create tables (already done by SQL script)
            $this->updateMigrationLog('create_tables', 'completed');

            // Step 2: Migrate classrooms
            $this->migrateClassrooms();

            // Step 3: Migrate student assignments
            $this->migrateStudentAssignments();

            // Step 4: Migrate attendance data
            $this->migrateAttendanceData();

            // Step 5: Migrate assignment data
            $this->migrateAssignmentData();

            // Step 6: Migrate grade data (handled through relationships)
            $this->updateMigrationLog('migrate_grade_data', 'completed', 0, 0, 'Handled through classroom relationships');

            // Step 7: Cleanup old structure (will be done manually later)
            $this->updateMigrationLog('cleanup_old_structure', 'pending', 0, 0, 'Manual cleanup required');

            // Step 8: Verify data integrity
            $this->verifyDataIntegrity();

            $this->conn->commit();
            return ['success' => true, 'message' => 'Migration completed successfully'];

        } catch (Exception $e) {
            $this->conn->rollback();
            error_log("Migration failed: " . $e->getMessage());
            return ['success' => false, 'message' => 'Migration failed: ' . $e->getMessage()];
        }
    }

    // Migrate existing classes to classrooms
    private function migrateClassrooms() {
        $this->updateMigrationLog('migrate_classrooms', 'running');

        try {
            // Get all unique combinations of kelas, semester, and tahun_ajaran from existing data
            $query = "SELECT DISTINCT 
                        k.id as kelas_id,
                        k.nama_kelas,
                        k.guru_id,
                        k.tingkat_id,
                        k.jurusan_id,
                        a.semester,
                        a.tahun_ajaran
                      FROM kelas k
                      CROSS JOIN (
                          SELECT DISTINCT semester, tahun_ajaran FROM absensi
                          UNION
                          SELECT DISTINCT semester, tahun_ajaran FROM tugas
                          UNION
                          SELECT DISTINCT semester, tahun_ajaran FROM nilai
                      ) a
                      ORDER BY k.nama_kelas, a.tahun_ajaran, a.semester";

            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $combinations = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $total = count($combinations);
            $processed = 0;

            foreach ($combinations as $combo) {
                // Create classroom for each combination
                $insert_query = "INSERT INTO ruang_kelas 
                               (nama_ruang_kelas, semester, tahun_ajaran, tingkat_id, jurusan_id, guru_wali_id, status)
                               VALUES (:nama, :semester, :tahun_ajaran, :tingkat_id, :jurusan_id, :guru_id, 'aktif')";

                $insert_stmt = $this->conn->prepare($insert_query);
                $insert_stmt->bindParam(':nama', $combo['nama_kelas']);
                $insert_stmt->bindParam(':semester', $combo['semester']);
                $insert_stmt->bindParam(':tahun_ajaran', $combo['tahun_ajaran']);
                $insert_stmt->bindParam(':tingkat_id', $combo['tingkat_id']);
                $insert_stmt->bindParam(':jurusan_id', $combo['jurusan_id']);
                $insert_stmt->bindParam(':guru_id', $combo['guru_id']);

                if ($insert_stmt->execute()) {
                    $processed++;
                }
            }

            $this->updateMigrationLog('migrate_classrooms', 'completed', $processed, $total);

        } catch (Exception $e) {
            $this->updateMigrationLog('migrate_classrooms', 'failed', 0, 0, $e->getMessage());
            throw $e;
        }
    }

    // Migrate student assignments
    private function migrateStudentAssignments() {
        $this->updateMigrationLog('migrate_student_assignments', 'running');

        try {
            // Assign students to classrooms based on their current class and active periods
            $query = "INSERT INTO siswa_ruang_kelas (siswa_id, ruang_kelas_id, tanggal_masuk, status)
                     SELECT DISTINCT 
                        s.id as siswa_id,
                        rk.id as ruang_kelas_id,
                        CURDATE() as tanggal_masuk,
                        'aktif' as status
                     FROM siswa s
                     INNER JOIN kelas k ON s.kelas_id = k.id
                     INNER JOIN ruang_kelas rk ON k.nama_kelas = rk.nama_ruang_kelas 
                                               AND k.tingkat_id = rk.tingkat_id 
                                               AND k.jurusan_id = rk.jurusan_id";

            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $processed = $stmt->rowCount();

            // Get total students for logging
            $count_query = "SELECT COUNT(*) as total FROM siswa";
            $count_stmt = $this->conn->prepare($count_query);
            $count_stmt->execute();
            $total = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];

            $this->updateMigrationLog('migrate_student_assignments', 'completed', $processed, $total);

        } catch (Exception $e) {
            $this->updateMigrationLog('migrate_student_assignments', 'failed', 0, 0, $e->getMessage());
            throw $e;
        }
    }

    // Migrate attendance data
    private function migrateAttendanceData() {
        $this->updateMigrationLog('migrate_attendance_data', 'running');

        try {
            // Update attendance records to reference classrooms
            $query = "UPDATE absensi a
                     INNER JOIN kelas k ON a.kelas_id = k.id
                     INNER JOIN ruang_kelas rk ON k.nama_kelas = rk.nama_ruang_kelas 
                                               AND a.semester = rk.semester 
                                               AND a.tahun_ajaran = rk.tahun_ajaran
                                               AND k.tingkat_id = rk.tingkat_id 
                                               AND k.jurusan_id = rk.jurusan_id
                     SET a.ruang_kelas_id = rk.id";

            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $processed = $stmt->rowCount();

            // Get total attendance records
            $count_query = "SELECT COUNT(*) as total FROM absensi";
            $count_stmt = $this->conn->prepare($count_query);
            $count_stmt->execute();
            $total = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];

            $this->updateMigrationLog('migrate_attendance_data', 'completed', $processed, $total);

        } catch (Exception $e) {
            $this->updateMigrationLog('migrate_attendance_data', 'failed', 0, 0, $e->getMessage());
            throw $e;
        }
    }

    // Migrate assignment data
    private function migrateAssignmentData() {
        $this->updateMigrationLog('migrate_assignment_data', 'running');

        try {
            // Update assignment records to reference classrooms
            $query = "UPDATE tugas t
                     INNER JOIN kelas k ON t.kelas_id = k.id
                     INNER JOIN ruang_kelas rk ON k.nama_kelas = rk.nama_ruang_kelas 
                                               AND t.semester = rk.semester 
                                               AND t.tahun_ajaran = rk.tahun_ajaran
                                               AND k.tingkat_id = rk.tingkat_id 
                                               AND k.jurusan_id = rk.jurusan_id
                     SET t.ruang_kelas_id = rk.id";

            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $processed = $stmt->rowCount();

            // Get total assignment records
            $count_query = "SELECT COUNT(*) as total FROM tugas";
            $count_stmt = $this->conn->prepare($count_query);
            $count_stmt->execute();
            $total = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];

            $this->updateMigrationLog('migrate_assignment_data', 'completed', $processed, $total);

        } catch (Exception $e) {
            $this->updateMigrationLog('migrate_assignment_data', 'failed', 0, 0, $e->getMessage());
            throw $e;
        }
    }

    // Verify data integrity
    private function verifyDataIntegrity() {
        $this->updateMigrationLog('verify_data_integrity', 'running');

        try {
            $issues = [];

            // Check if all students are assigned to classrooms
            $query = "SELECT COUNT(*) as count FROM siswa s 
                     WHERE s.id NOT IN (SELECT siswa_id FROM siswa_ruang_kelas WHERE status = 'aktif')";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $unassigned_students = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

            if ($unassigned_students > 0) {
                $issues[] = "$unassigned_students students not assigned to any classroom";
            }

            // Check if all attendance records have classroom references
            $query = "SELECT COUNT(*) as count FROM absensi WHERE ruang_kelas_id IS NULL";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $orphaned_attendance = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

            if ($orphaned_attendance > 0) {
                $issues[] = "$orphaned_attendance attendance records without classroom reference";
            }

            // Check if all assignment records have classroom references
            $query = "SELECT COUNT(*) as count FROM tugas WHERE ruang_kelas_id IS NULL";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $orphaned_assignments = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

            if ($orphaned_assignments > 0) {
                $issues[] = "$orphaned_assignments assignment records without classroom reference";
            }

            if (empty($issues)) {
                $this->updateMigrationLog('verify_data_integrity', 'completed', 0, 0, 'All data integrity checks passed');
            } else {
                $error_message = implode('; ', $issues);
                $this->updateMigrationLog('verify_data_integrity', 'completed', 0, 0, "Issues found: $error_message");
            }

        } catch (Exception $e) {
            $this->updateMigrationLog('verify_data_integrity', 'failed', 0, 0, $e->getMessage());
            throw $e;
        }
    }

    // Create database backup before migration
    public function createBackup() {
        try {
            $backup_dir = __DIR__ . '/../database/backups/';
            if (!is_dir($backup_dir)) {
                mkdir($backup_dir, 0755, true);
            }

            $backup_file = $backup_dir . 'pre_classroom_migration_' . date('Y-m-d_H-i-s') . '.sql';
            
            // This is a simplified backup - in production, you'd want to use mysqldump
            $tables_query = "SHOW TABLES";
            $tables_stmt = $this->conn->prepare($tables_query);
            $tables_stmt->execute();
            
            $backup_content = "-- Backup created before classroom migration\n";
            $backup_content .= "-- Date: " . date('Y-m-d H:i:s') . "\n\n";
            $backup_content .= "SET FOREIGN_KEY_CHECKS=0;\n\n";

            while ($table = $tables_stmt->fetch(PDO::FETCH_NUM)) {
                $table_name = $table[0];
                
                // Get table structure
                $structure_query = "SHOW CREATE TABLE `$table_name`";
                $structure_stmt = $this->conn->prepare($structure_query);
                $structure_stmt->execute();
                $structure = $structure_stmt->fetch(PDO::FETCH_ASSOC);
                
                $backup_content .= "DROP TABLE IF EXISTS `$table_name`;\n";
                $backup_content .= $structure['Create Table'] . ";\n\n";
            }

            $backup_content .= "SET FOREIGN_KEY_CHECKS=1;\n";

            file_put_contents($backup_file, $backup_content);
            
            return ['success' => true, 'file' => $backup_file];

        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    // Execute the initial SQL migration script
    public function executeInitialMigration() {
        try {
            // Try simple migration script first
            $sql_file = __DIR__ . '/../database/classroom_migration_simple.sql';

            if (!file_exists($sql_file)) {
                throw new Exception("Migration SQL file not found: " . $sql_file);
            }

            $sql_content = file_get_contents($sql_file);

            // Split SQL into individual statements
            $statements = array_filter(array_map('trim', explode(';', $sql_content)));

            $executed = 0;
            $errors = [];

            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^--/', $statement)) {
                    try {
                        $this->conn->exec($statement);
                        $executed++;
                    } catch (PDOException $e) {
                        // Log error but continue with other statements
                        $error_msg = $e->getMessage();

                        // Skip safe errors that we can ignore
                        if (strpos($error_msg, 'already exists') === false &&
                            strpos($error_msg, 'Duplicate column name') === false &&
                            strpos($error_msg, 'Duplicate key name') === false &&
                            strpos($error_msg, 'Duplicate entry') === false) {
                            $errors[] = "Statement failed: " . substr($statement, 0, 100) . "... - " . $error_msg;
                        }
                    }
                }
            }

            // Now add the columns to existing tables
            $this->addColumnsToExistingTables();

            $message = "Migration structure created successfully. Executed $executed statements.";
            if (!empty($errors)) {
                $message .= " Some non-critical errors occurred: " . implode("; ", array_slice($errors, 0, 3));
            }

            return ['success' => true, 'message' => $message];

        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Migration failed: ' . $e->getMessage()];
        }
    }

    // Add columns to existing tables safely
    private function addColumnsToExistingTables() {
        try {
            // Add kelas_id_backup to siswa table if it doesn't exist
            $check_query = "SHOW COLUMNS FROM siswa LIKE 'kelas_id_backup'";
            $stmt = $this->conn->query($check_query);
            if ($stmt->rowCount() == 0) {
                $this->conn->exec("ALTER TABLE siswa ADD COLUMN kelas_id_backup int(11) DEFAULT NULL");
                $this->conn->exec("UPDATE siswa SET kelas_id_backup = kelas_id WHERE kelas_id IS NOT NULL");
            }

            // Add ruang_kelas_id to absensi table if it doesn't exist
            $check_query = "SHOW COLUMNS FROM absensi LIKE 'ruang_kelas_id'";
            $stmt = $this->conn->query($check_query);
            if ($stmt->rowCount() == 0) {
                $this->conn->exec("ALTER TABLE absensi ADD COLUMN ruang_kelas_id int(11) DEFAULT NULL");
                $this->conn->exec("ALTER TABLE absensi ADD KEY fk_absensi_ruang_kelas (ruang_kelas_id)");
            }

            // Add ruang_kelas_id to tugas table if it doesn't exist
            $check_query = "SHOW COLUMNS FROM tugas LIKE 'ruang_kelas_id'";
            $stmt = $this->conn->query($check_query);
            if ($stmt->rowCount() == 0) {
                $this->conn->exec("ALTER TABLE tugas ADD COLUMN ruang_kelas_id int(11) DEFAULT NULL");
                $this->conn->exec("ALTER TABLE tugas ADD KEY fk_tugas_ruang_kelas (ruang_kelas_id)");
            }

        } catch (PDOException $e) {
            // Log error but don't fail the migration
            error_log("Error adding columns: " . $e->getMessage());
        }
    }
}
