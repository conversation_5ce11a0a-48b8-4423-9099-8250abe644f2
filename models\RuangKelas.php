<?php
require_once __DIR__ . '/../config/database.php';

class RuangKelas {
    private $conn;
    private $table_name = "ruang_kelas";
    private $assignment_table = "siswa_ruang_kelas";

    public $id;
    public $nama_ruang_kelas;
    public $deskripsi;
    public $semester;
    public $tahun_ajaran;
    public $tingkat_id;
    public $jurusan_id;
    public $guru_wali_id;
    public $kapasitas_maksimal;
    public $status;
    public $created_at;
    public $updated_at;

    // Additional properties for joined data
    public $nama_tingkat;
    public $nama_jurusan;
    public $nama_guru_wali;
    public $jumlah_siswa;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // Get all classrooms with optional filtering
    public function getAll($semester = null, $tahun_ajaran = null) {
        $query = "SELECT rk.*, 
                         t.nama_tingkat,
                         j.nama_jurusan,
                         g.nama_leng<PERSON>p as nama_guru_wali,
                         COUNT(srk.id) as jumlah_siswa
                  FROM " . $this->table_name . " rk
                  LEFT JOIN tingkat t ON rk.tingkat_id = t.id
                  LEFT JOIN jurusan j ON rk.jurusan_id = j.id
                  LEFT JOIN guru g ON rk.guru_wali_id = g.id
                  LEFT JOIN " . $this->assignment_table . " srk ON rk.id = srk.ruang_kelas_id AND srk.status = 'aktif'";
        
        $conditions = [];
        if ($semester !== null) {
            $conditions[] = "rk.semester = :semester";
        }
        if ($tahun_ajaran !== null) {
            $conditions[] = "rk.tahun_ajaran = :tahun_ajaran";
        }
        
        if (!empty($conditions)) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }
        
        $query .= " GROUP BY rk.id ORDER BY rk.nama_ruang_kelas ASC";
        
        $stmt = $this->conn->prepare($query);
        
        if ($semester !== null) {
            $stmt->bindParam(":semester", $semester);
        }
        if ($tahun_ajaran !== null) {
            $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        }
        
        $stmt->execute();
        return $stmt;
    }

    // Get classroom by ID with full details
    public function getById($id) {
        $query = "SELECT rk.*, 
                         t.nama_tingkat,
                         j.nama_jurusan,
                         g.nama_lengkap as nama_guru_wali,
                         COUNT(srk.id) as jumlah_siswa
                  FROM " . $this->table_name . " rk
                  LEFT JOIN tingkat t ON rk.tingkat_id = t.id
                  LEFT JOIN jurusan j ON rk.jurusan_id = j.id
                  LEFT JOIN guru g ON rk.guru_wali_id = g.id
                  LEFT JOIN " . $this->assignment_table . " srk ON rk.id = srk.ruang_kelas_id AND srk.status = 'aktif'
                  WHERE rk.id = :id
                  GROUP BY rk.id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Create new classroom
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  (nama_ruang_kelas, deskripsi, semester, tahun_ajaran, tingkat_id, jurusan_id, guru_wali_id, kapasitas_maksimal, status)
                  VALUES
                  (:nama_ruang_kelas, :deskripsi, :semester, :tahun_ajaran, :tingkat_id, :jurusan_id, :guru_wali_id, :kapasitas_maksimal, :status)";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->nama_ruang_kelas = htmlspecialchars(strip_tags($this->nama_ruang_kelas));
        $this->deskripsi = htmlspecialchars(strip_tags($this->deskripsi));
        $this->semester = htmlspecialchars(strip_tags($this->semester));
        $this->tahun_ajaran = htmlspecialchars(strip_tags($this->tahun_ajaran));
        $this->status = $this->status ?: 'aktif';
        $this->kapasitas_maksimal = $this->kapasitas_maksimal ?: 40;

        // Bind parameters
        $stmt->bindParam(":nama_ruang_kelas", $this->nama_ruang_kelas);
        $stmt->bindParam(":deskripsi", $this->deskripsi);
        $stmt->bindParam(":semester", $this->semester);
        $stmt->bindParam(":tahun_ajaran", $this->tahun_ajaran);
        $stmt->bindParam(":tingkat_id", $this->tingkat_id);
        $stmt->bindParam(":jurusan_id", $this->jurusan_id);
        $stmt->bindParam(":guru_wali_id", $this->guru_wali_id);
        $stmt->bindParam(":kapasitas_maksimal", $this->kapasitas_maksimal);
        $stmt->bindParam(":status", $this->status);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }
        return false;
    }

    // Update classroom
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET nama_ruang_kelas = :nama_ruang_kelas,
                      deskripsi = :deskripsi,
                      semester = :semester,
                      tahun_ajaran = :tahun_ajaran,
                      tingkat_id = :tingkat_id,
                      jurusan_id = :jurusan_id,
                      guru_wali_id = :guru_wali_id,
                      kapasitas_maksimal = :kapasitas_maksimal,
                      status = :status
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->nama_ruang_kelas = htmlspecialchars(strip_tags($this->nama_ruang_kelas));
        $this->deskripsi = htmlspecialchars(strip_tags($this->deskripsi));
        $this->semester = htmlspecialchars(strip_tags($this->semester));
        $this->tahun_ajaran = htmlspecialchars(strip_tags($this->tahun_ajaran));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind parameters
        $stmt->bindParam(":nama_ruang_kelas", $this->nama_ruang_kelas);
        $stmt->bindParam(":deskripsi", $this->deskripsi);
        $stmt->bindParam(":semester", $this->semester);
        $stmt->bindParam(":tahun_ajaran", $this->tahun_ajaran);
        $stmt->bindParam(":tingkat_id", $this->tingkat_id);
        $stmt->bindParam(":jurusan_id", $this->jurusan_id);
        $stmt->bindParam(":guru_wali_id", $this->guru_wali_id);
        $stmt->bindParam(":kapasitas_maksimal", $this->kapasitas_maksimal);
        $stmt->bindParam(":status", $this->status);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    // Delete classroom (only if no students assigned)
    public function delete() {
        // Check if classroom has students
        $check_query = "SELECT COUNT(*) as count FROM " . $this->assignment_table . " WHERE ruang_kelas_id = :id AND status = 'aktif'";
        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->bindParam(":id", $this->id);
        $check_stmt->execute();
        $result = $check_stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['count'] > 0) {
            return false; // Cannot delete classroom with active students
        }

        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $this->id = htmlspecialchars(strip_tags($this->id));
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    // Get students in a classroom
    public function getStudents($ruang_kelas_id, $status = 'aktif') {
        $query = "SELECT s.*, srk.tanggal_masuk, srk.tanggal_keluar, srk.status as status_ruang_kelas, srk.keterangan
                  FROM siswa s
                  INNER JOIN " . $this->assignment_table . " srk ON s.id = srk.siswa_id
                  WHERE srk.ruang_kelas_id = :ruang_kelas_id";
        
        if ($status !== null) {
            $query .= " AND srk.status = :status";
        }
        
        $query .= " ORDER BY s.nama_siswa ASC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":ruang_kelas_id", $ruang_kelas_id);
        
        if ($status !== null) {
            $stmt->bindParam(":status", $status);
        }
        
        $stmt->execute();
        return $stmt;
    }

    // Assign student to classroom
    public function assignStudent($siswa_id, $ruang_kelas_id, $tanggal_masuk = null) {
        // Check if student is already assigned to this classroom
        $check_query = "SELECT id FROM " . $this->assignment_table . " 
                       WHERE siswa_id = :siswa_id AND ruang_kelas_id = :ruang_kelas_id AND status = 'aktif'";
        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->bindParam(":siswa_id", $siswa_id);
        $check_stmt->bindParam(":ruang_kelas_id", $ruang_kelas_id);
        $check_stmt->execute();
        
        if ($check_stmt->rowCount() > 0) {
            return false; // Student already assigned
        }

        $tanggal_masuk = $tanggal_masuk ?: date('Y-m-d');
        
        $query = "INSERT INTO " . $this->assignment_table . "
                  (siswa_id, ruang_kelas_id, tanggal_masuk, status)
                  VALUES
                  (:siswa_id, :ruang_kelas_id, :tanggal_masuk, 'aktif')";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":ruang_kelas_id", $ruang_kelas_id);
        $stmt->bindParam(":tanggal_masuk", $tanggal_masuk);

        return $stmt->execute();
    }

    // Remove student from classroom
    public function removeStudent($siswa_id, $ruang_kelas_id, $status = 'pindah', $keterangan = null) {
        $query = "UPDATE " . $this->assignment_table . "
                  SET status = :status,
                      tanggal_keluar = :tanggal_keluar,
                      keterangan = :keterangan
                  WHERE siswa_id = :siswa_id AND ruang_kelas_id = :ruang_kelas_id AND status = 'aktif'";

        $stmt = $this->conn->prepare($query);
        $tanggal_keluar = date('Y-m-d');
        
        $stmt->bindParam(":status", $status);
        $stmt->bindParam(":tanggal_keluar", $tanggal_keluar);
        $stmt->bindParam(":keterangan", $keterangan);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":ruang_kelas_id", $ruang_kelas_id);

        return $stmt->execute();
    }

    // Get available students (not assigned to any classroom in the same semester/year)
    public function getAvailableStudents($semester, $tahun_ajaran) {
        $query = "SELECT s.*
                  FROM siswa s
                  WHERE s.id NOT IN (
                      SELECT DISTINCT srk.siswa_id
                      FROM " . $this->assignment_table . " srk
                      INNER JOIN " . $this->table_name . " rk ON srk.ruang_kelas_id = rk.id
                      WHERE rk.semester = :semester 
                      AND rk.tahun_ajaran = :tahun_ajaran 
                      AND srk.status = 'aktif'
                  )
                  ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    // Get classroom by student and period
    public function getByStudentAndPeriod($siswa_id, $semester, $tahun_ajaran) {
        $query = "SELECT rk.*, srk.tanggal_masuk, srk.status as status_assignment
                  FROM " . $this->table_name . " rk
                  INNER JOIN " . $this->assignment_table . " srk ON rk.id = srk.ruang_kelas_id
                  WHERE srk.siswa_id = :siswa_id 
                  AND rk.semester = :semester 
                  AND rk.tahun_ajaran = :tahun_ajaran
                  AND srk.status = 'aktif'";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Get total count of classrooms
    public function getCount($semester = null, $tahun_ajaran = null) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        
        $conditions = [];
        if ($semester !== null) {
            $conditions[] = "semester = :semester";
        }
        if ($tahun_ajaran !== null) {
            $conditions[] = "tahun_ajaran = :tahun_ajaran";
        }
        
        if (!empty($conditions)) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }
        
        $stmt = $this->conn->prepare($query);
        
        if ($semester !== null) {
            $stmt->bindParam(":semester", $semester);
        }
        if ($tahun_ajaran !== null) {
            $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        }
        
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }
}
